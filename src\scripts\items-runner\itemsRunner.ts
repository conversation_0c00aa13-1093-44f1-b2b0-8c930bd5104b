import { StatefulScript } from '../../api/core/script/statefulScript'
import { Skill } from '../../api/game/skill'
import { AccountData } from '../../api/model/accountData'
import { Item } from '../../api/model/item'
import { TradePackage } from '../../api/model/tradePackage'
import { GiveToMuleState } from '../../api/script-utils/mule/giveMuleStrategy'
import { ResupplyMuleState } from '../../api/script-utils/mule/resupplyMuleStrategy'
import { BehavioralSleep } from '../../api/utils/behavior-fingerprinting/behavioralSleep'
import { formatTime, hourRatio } from '../../api/utils/utils'
import { BotSettings } from '../../botSettings'
import { MuleReceiver } from '../muling/muleReceiver'
import { MainItemsRunner } from './states/mainItemsRunner'


export class ItemsRunner extends StatefulScript {
    recentLevel: number
  
    constructor() {
        super()
        this.loopInterval = 200
    }

    onStart(): void {
        this.initWithState(new MainItemsRunner(this.spot))
    }

    onDraw(canvas: any, paint: any): void {
        this.drawText('Items Runner [' + this.currentState.name + ']')
        this.drawText('Runtime: ' + this.progressTracker.getTimeRunning())
        this.drawText('Actions: ' + this.getActionsDone() + '(' + hourRatio(this.progressTracker.startTime, this.getActionsDone()) + ')')
        if (BehavioralSleep.sleepingUntil != -1) {
            this.drawText(`Sleep: ${BehavioralSleep.sleepReason} (${formatTime(BehavioralSleep.sleepingUntil - Date.now())})`)
        }
    }

    getActionsDone() {
        this.recentLevel = Skill.ATTACK.getCurrentLevel() // Change skill as needed
        return Math.floor(this.getExpGained() / 10) // Change XP per action as needed
    }

    getExpGained() {
        return this.progressTracker.getExpGained(Skill.ATTACK) // Change skill as needed
    }

    runtimeInfo(): string {
        return `<th>Level: ${Skill.ATTACK.getCurrentLevel()}</th><th>Actions: ${this.getActionsDone() + '(' + hourRatio(this.progressTracker.startTime, this.getActionsDone()) + ')'}</th> <th>${this.currentState?.name}</th>`
    }
}

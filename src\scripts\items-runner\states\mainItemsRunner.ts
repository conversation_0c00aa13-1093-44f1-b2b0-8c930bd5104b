import { State, createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Inventory } from '../../../api/game/inventory'
import { Walking } from '../../../api/game/walking'
import { WorldHopping } from '../../../api/game/worldHopping'
import { ItemId } from '../../../data/itemId'
import { createGeState } from '../../../api/script-utils/states/geStates'
import { GeAction } from '../../../api/game/geAction'
import { ResupplyMuleState } from '../../../api/script-utils/mule/resupplyMuleStrategy'
import { GiveToMuleState } from '../../../api/script-utils/mule/giveMuleStrategy'
import { Item } from '../../../api/model/item'
import { TradePackage } from '../../../api/model/tradePackage'
import { BotSettings } from '../../../botSettings'
import { MuleReceiver } from '../../muling/muleReceiver'
import { addEquipmentManager } from '../../../api/script-utils/states/equipmentStates'
import { GetRingOfTheElementsState } from '../../mud-crafter/states/mudCrafterGe'
import { Teleport } from '../../../data/teleport'
import { Equipment } from '../../../api/game/equipment'
import { Varps } from '../../../api/game/varps'
import { ItemPredicate } from '../../../data/itemPredicates'
import { Player } from '../../../api/wrappers/player'
import { Time } from '../../../api/utils/time'
import { Tile } from '../../../api/model/tile'
import { GameObjects } from '../../../api/game/gameObjects'
import { Redis } from '../../../api/utils/redis'
import { log } from '../../../api/utils/utils'

export type ItemsRunnerSpot = 'pure-essence'


export class MainItemsRunner extends State {

    resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 308)
    giveToMule = new GiveToMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_GIVE), BotSettings.tradeWorldP2p, [new Item(995, 700_000)], [new Item(995, ***********)])

    // Slot management properties
    private currentSlotHost: string | null = null
    private lastHeartbeat: number = 0
    private lastHostCheck: number = 0
    private slotCheckInterval = 30000 // Check for available slots every 30 seconds
    private hostCheckInterval = 60000 // Check if host is still available every 60 seconds

    onDraw(canvas: any, paint: any): void {
        this.drawText(`Slot Host: ${this.currentSlotHost || 'None'}`)
        this.drawText(`Has Slot: ${this.hasSlot() ? 'Yes' : 'No'}`)

        try {
            const task = Redis.getJson("delivery:tasks:mud-runes:123456")
            this.drawText(`Task: ` + JSON.stringify(task))
        } catch (e) {
            this.drawText(`Task: Not found`)
        }
    }

    get spot() {
        return "pure-essence"
    }

    onFirstExecute(): void {
        addEquipmentManager(this, [Equipment.SLOT_HELM])

        // Clean up any existing slots on startup
        this.releaseSlot()
    }

    onBackgroundAction(): void {
        Walking.setRunAuto()
        this.manageSlots()
    }

    onAction(): void {
        if (!WorldHopping.switchToP2pExcept()) {
            return
        }

        this.setState(this.bankingState)
    }

    bankingState = createState('Banking', () => {
        Walking.setRunAuto()

        if (Player.getRunEnergy() < 20) {
            this.setState(this.drinkStamina)
            return
        }

        if (!Bank.openNearest()) {
            return
        }

        // Deposit all except what we need
        Bank.depositAllExceptPredicate((i) =>
            [ItemId.RING_OF_THE_ELEMENTS_26818, ItemId.PURE_ESSENCE].includes(i.id) ||
            Teleport.ringOfDuelingPredicate(i)
        )

        // Equip earth tiara (needed to enter mud altar)
        if (!Equipment.withdrawAndEquip(ItemId.EARTH_TIARA, () => this.geState)) {
            return
        }

        // Equip ring of dueling
        if (!Equipment.withdrawAndEquipByPredicate(Teleport.ringOfDuelingPredicate, () => this.geState)) {
            return
        }

        // Withdraw ring of elements
        if (!Withdraw.id(ItemId.RING_OF_THE_ELEMENTS_26818, 1).minimumAmount(1).orState(() => new GetRingOfTheElementsState(this, this.resupply)).ensureSpace().withdraw()) {
            return
        }

        // Withdraw pure essence
        if (!Withdraw.id(ItemId.PURE_ESSENCE, 25).minimumAmount(25).orState(() => this.geState).ensureSpace().withdraw()) {
            return
        }

        this.setState(this.deliveryState)
    })

    drinkStamina = createState('Drinking stamina potion', () => {
        if (Player.getRunEnergy() > 80 && Player.isStaminaPotionActive()) {
            this.setState(this.bankingState)
            return
        }

        if (!Bank.openNearest()) {
            return
        }

        if (!Withdraw.predicate(ItemPredicate.staminaPotion, 1).orState(() => this.geState).withdraw()) {
            return
        }

        if (Bank.isOpen()) {
            Inventory.get(Inventory.bank).getByPredicate(ItemPredicate.staminaPotion)?.click(1007, 9)
        } else {
            Inventory.getByPredicate(ItemPredicate.staminaPotion)?.click(57, 2)
        }

        Time.sleep(400, 1000)
    })

    deliveryState = createState('Delivering pure essence', () => {
        // If no pure essence left, go back to banking
        if (!Inventory.contains(ItemId.PURE_ESSENCE)) {
            this.setState(this.bankingState)
            return
        }

        if (new Tile(2658, 4839, 0).distance() < 20) {
            if(!Walking.walkTo(new Tile(2656, 4839, 0), 1)) return

            return
        }

        // Check if we're near the earth altar entrance
        if (new Tile(3308, 3474, 0).distance() > 30) {
            // Use ring of elements to teleport to earth altar
            Teleport.ringOfTheElementsEarth.process()
            return
        } else {
            // Walk to the earth altar entrance and enter it
            const obj = GameObjects.getNearest(new Tile(3308, 3474, 0), (o) => o.realId == 29099, 10)
            if (obj == null && !Walking.walkTo(new Tile(3308, 3474, 0), 6)) return

            obj?.click(3)
            Time.sleep(() => new Tile(2658, 4839, 0).distance() < 30)
        }
    })

    geState = createGeState(
        () => this,
        () => this.resupply,
        [
            GeAction.item(ItemId.PURE_ESSENCE, 3000).gePrice(1.12, 5).buy(),
            GeAction.item(ItemId.RING_OF_DUELING8, 40).gePrice(1.12, 1000).buy(),
            GeAction.item(ItemId.STAMINA_POTION4, 15).gePrice(1.1, 1000).buy(),
            GeAction.item(ItemId.EARTH_TIARA, 1).gePrice(1.12, 1555).withEq().buy(),
        ], 'Items Runner GE'
    )

    // Slot management methods
    private manageSlots(): void {
        const now = Date.now()

        // Try to take a slot if we don't have one
        if (!this.hasSlot()) {
            if (now - this.lastHostCheck > this.slotCheckInterval) {
                this.tryTakeSlot()
                this.lastHostCheck = now
            }
        } else {
            // Send heartbeat every 10 seconds
            if (now - this.lastHeartbeat > 10000) {
                this.sendHeartbeat()
                this.lastHeartbeat = now
            }

            // Check if host is still available every 60 seconds
            if (now - this.lastHostCheck > this.hostCheckInterval) {
                this.checkHostAvailability()
                this.lastHostCheck = now
            }
        }
    }

    private hasSlot(): boolean {
        if (!this.currentSlotHost) return false

        try {
            const slaveUsername = Player.local?.username
            if (!slaveUsername) return false

            const slotKey = `delivery:slots:${this.spot}:${this.currentSlotHost}`
            const slotData = Redis.get(slotKey)

            if (!slotData) return false

            const parsedData = JSON.parse(slotData)
            return parsedData.slaveUsername === slaveUsername
        } catch (e) {
            log('[Slot Management] Error checking slot:', e)
            return false
        }
    }

    private tryTakeSlot(): void {
        try {
            const slaveUsername = Player.local?.username
            if (!slaveUsername) return

            // Get all available hosts
            const availableHosts = this.getAvailableHosts()

            for (const hostUsername of availableHosts) {
                const slotKey = `delivery:slots:${this.spot}:${hostUsername}`
                const slotData = { slaveUsername }

                // Try to take the slot with TTL of 60 seconds
                const success = Redis.setnx(slotKey, JSON.stringify(slotData), 60)

                if (success) {
                    this.currentSlotHost = hostUsername
                    this.lastHeartbeat = Date.now()
                    log(`[Slot Management] Successfully took slot for host: ${hostUsername}`)
                    return
                }
            }

            log('[Slot Management] No available slots found')
        } catch (e) {
            log('[Slot Management] Error taking slot:', e)
        }
    }

    private getAvailableHosts(): string[] {
        try {
            // Get all slot-info keys for this task type
            const pattern = `delivery:slots-info:${this.spot}:*`
            const keys = Redis.keys(pattern)

            const availableHosts: string[] = []

            for (const key of keys) {
                // Extract host username from key
                const hostUsername = key.split(':').pop()
                if (!hostUsername) continue

                // Check if this host has available slots
                const slotInfoData = Redis.get(key)
                if (!slotInfoData) continue

                const slotInfo = JSON.parse(slotInfoData)
                const maxSlots = slotInfo.maxSlots || 1

                // Count current slots taken for this host
                const currentSlotsPattern = `delivery:slots:${this.spot}:${hostUsername}`
                const currentSlots = Redis.exists(currentSlotsPattern) ? 1 : 0

                if (currentSlots < maxSlots) {
                    availableHosts.push(hostUsername)
                }
            }

            return availableHosts
        } catch (e) {
            log('[Slot Management] Error getting available hosts:', e)
            return []
        }
    }

    private sendHeartbeat(): void {
        if (!this.currentSlotHost) return

        try {
            const slaveUsername = Player.local?.username
            if (!slaveUsername) return

            const slotKey = `delivery:slots:${this.spot}:${this.currentSlotHost}`
            const slotData = { slaveUsername }

            // Refresh the TTL to 60 seconds
            Redis.set(slotKey, JSON.stringify(slotData), 60)
            log(`[Slot Management] Heartbeat sent for host: ${this.currentSlotHost}`)
        } catch (e) {
            log('[Slot Management] Error sending heartbeat:', e)
        }
    }

    private checkHostAvailability(): void {
        if (!this.currentSlotHost) return

        try {
            const slotInfoKey = `delivery:slots-info:${this.spot}:${this.currentSlotHost}`

            if (!Redis.exists(slotInfoKey)) {
                log(`[Slot Management] Host ${this.currentSlotHost} is no longer available, releasing slot`)
                this.releaseSlot()
            }
        } catch (e) {
            log('[Slot Management] Error checking host availability:', e)
        }
    }

    private releaseSlot(): void {
        if (!this.currentSlotHost) return

        try {
            const slotKey = `delivery:slots:${this.spot}:${this.currentSlotHost}`
            Redis.del(slotKey)

            log(`[Slot Management] Released slot for host: ${this.currentSlotHost}`)
            this.currentSlotHost = null
            this.lastHeartbeat = 0
        } catch (e) {
            log('[Slot Management] Error releasing slot:', e)
        }
    }

    /**
     * Public method to clean up slots when script stops
     */
    public cleanup(): void {
        this.releaseSlot()
    }
}

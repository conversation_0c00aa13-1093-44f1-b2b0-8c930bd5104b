import randomItem from 'random-item'
import { Input } from '../api/core/input/input'
import { LowCpu } from '../api/core/lowCpu'
import { BotScript } from '../api/core/script/botScript'
import { Inventory } from '../api/game/inventory'
import { Trade } from '../api/game/trade'
import { Widgets } from '../api/game/widgets'
import { WorldHopping } from '../api/game/worldHopping'
import { WorldToScreen } from '../api/game/worldToScreen'
import { AccountData } from '../api/model/accountData'
import { distinctBy } from '../api/utils/arrayExtensions'
import { getCache, getOrSetCache, putCache } from '../api/utils/cache'
import { Random } from '../api/utils/random'
import { Time } from '../api/utils/time'
import { log, createRunnable, randomString, startLoop, toJavaColor, createOnClickListener, startNewThread, readJagString, toHex, runOnUtilityThread, javaPerform, scheduleOnMainThread } from '../api/utils/utils'
import { WebUtils } from '../api/utils/webUtils'
import { Client } from '../api/wrappers/client'
import { Game } from '../api/wrappers/game'
import { BotSettings } from '../botSettings'
import { LowCpuSetter } from './background-tasks/lowCpuSetter'
import { Bot } from './bot'
import { CanvasHandler } from './canvasHandler'
import { Debuggers } from './debuggers'
import { ScriptManager } from './scriptManager'
import { GameWorldType } from '../api/game/gameWorld'
import { CameraMover } from '../api/utils/cameraMover'
import { Bank } from '../api/game/bank'
import { MuleTrader } from '../scripts/utility/MuleTrader'
import { LoginEvent } from '../api/core/script/game-random-events/loginEvent'
import { ScriptHandler } from '../api/core/script/scriptHandler'
import { testClick } from '../api/model/point'
import { PersistentLogs } from '../api/utils/persistentLogs'
import { logSentry } from '../api/utils/sentry'
import { checkForUpdates, getInstalledBundleHash, getNewestBundleHash, updateBundle } from './updater'
import { Player } from '../api/wrappers/player'
import { NpcComposite } from '../api/wrappers/npcComposite'
import { NpcDefinitions } from '../data/npcDefinitions'
import { Players } from '../api/game/players'
import { ProxyHandler } from './proxyHandler'
import { Redis } from '../api/utils/redis'

export class Ui {
    static context: any
    static layout: any
    static debugLayout: any // New layout for debug buttons
    static views: any[] = []
    static canvasView: any
    static uiUpdateThread: any

    static addUiComponents() {
        // Main (bottom) buttons
        log('Add button 1')
        this.addButtonToMain('Start', (btn) => {
            if (!Bot.initialized) {
                log('Wait for bot to load.')
                return
            }
            if (Bot.scriptHandler.currentScript != null) {
                Bot.scriptHandler.stop()
                BotSettings.autostartEnabled = false
            } else {
                ScriptManager.start()
            }
        })
        log('Add button 2')
        this.addButtonToMain('Low cpu', () => {
            LowCpu.isEnabled ??= true
            if (LowCpu.isEnabled) LowCpuSetter.stop()
            else LowCpuSetter.start()
        })

        this.addButtonToMain('Logs', () => {
            CanvasHandler.drawLogs = !CanvasHandler.drawLogs
        })

        this.addButtonToMain('Debug', () => {
            Ui.toggleDebugVisibility()
        })
        this.addButtonToMain('test redis', () => {
            //  Redis.set('delivery:tasks:mud-runes:123456', '123')
            log("Redis get: " + Redis.get("delivery:tasks:mud-runes:123456"))
            // log("Redis keys: " + Redis.keys("*"))
        })
        // this.addButtonToMain('test', () => {
           
        //    // const attempt = getOrSetCache("attempt", 0)
        //    // putCache("attempt",(attempt + 1))
        // //    log("", (int64("282574488338432").shr(20).and(0xffff).valueOf() | 0))
        // //    log("", (int64("281470681743360").shr(20).and(0xffff).valueOf() | 0))
        // //    log("", (int64("28862180294655").shr(20).and(0xffff).valueOf() | 0))
        // //    log("", (int64("31065498451664").shr(20).and(0xffff).valueOf() | 0))
        // //    log("", (int64("281414005824564").shr(20).and(0xffff).valueOf() | 0))
        // //    log("", (int64("282574488338432").shr(20).and(0xffff).valueOf() | 0))
        // //    log("", (int64("137700414152680").shr(20).and(0xffff).valueOf() | 0))
        // //    log("", (int64("1399691187060736").shr(20).and(0xffff).valueOf() | 0))



        //     const inputWidget = ptr("0x")
        //     for(let i = 0; i < 450; i++) {
        //             const def = inputWidget.add(0x4 * i).readLong()
        //             const val = (int64(def).shr(20).and(0xffff).valueOf() | 0)
        //             log(toHex(i * 0x4), val)
        //     }1
        //  })
        
        // this.addButtonToMain('world', () => {
        //     Game.setGameWorld(randomItem(Game.worldSelectorWorlds.filter((w) => w.id == 308)))
        // })

        //  this.addButtonToMain('test', () => {
        //     // PersistentLogs.writeLine("Test")
        //     // logSentry("Siema test 1234")

        //     log("Before utility thread.")
        //     runOnUtilityThread(() => {
        //         log("Siema test")
        //     })

        //     // Inventory.get().items[0].moveToSlot(25)
        //     // testClick(889, 256)
        //     // const scalex = getCache("scaleXUi")
        //     // putCache("scaleXUi", scalex + 0.01)
        //     // log("scaleXUi", scalex)
        // })

        this.addButtonToMain('mule', () => {
            Bot.scriptHandler.loginEvent.enabled = true

            if (Bot.scriptHandler.currentScript != null) {
                Bot.scriptHandler.stop()
                BotSettings.autostartEnabled = false
            }
            Bot.scriptHandler.startQueue(new MuleTrader())
        })

        this.addButtonToMain('Offer inv', () => {
            BotSettings.useBehavioralPointGeneration = true

            let clicks = 0
            distinctBy(Inventory.get().items, (i) => i.id).forEach((item) => {
                Trade.offer(item.id, item.amount, false)
                clicks++
                if (clicks >= 4) {
                    Time.sleep(700, 900)
                    clicks = 0
                }
            })
        })

        // Debug (top-left) buttons
        this.addDebugButton('Game', () => this.toggleDebugOption('isGameDebug'))
        this.addDebugButton('NPCs', () => this.toggleDebugOption('isNpcsDebug'))
        this.addDebugButton('Players', () => this.toggleDebugOption('isPlayersDebug'))
        this.addDebugButton('Objects', () => this.toggleDebugOption('isGameObjectsDebug'))
        this.addDebugButton('Inventory', () => this.toggleDebugOption('isInventoryDebug'))
        this.addDebugButton('Varbits', () => this.toggleDebugOption('isVarbitsDebug'))
        this.addDebugButton('Collision', () => this.toggleDebugOption('isCollisionFlagsDebug'))
        this.addDebugButton('Device Info', () => this.toggleDebugOption('isDeviceInfoDebug'))
        // this.addDebugButton('Logs', () => this.toggleDebugOption('isPersistentLogsDebug'))
        // this.addDebugButton('Firebase', () => this.toggleDebugOption('isFirebaseDebug'))
        this.addDebugButton('Memory', () => this.toggleDebugOption('isMemoryDebug'))
        this.addDebugButton('Widgets', () => this.toggleDebugOption('isWidgetsDebug'))
        this.addDebugButton('Ground Items', () => this.toggleDebugOption('isGroundItemsDebug'))
        this.addDebugButton('Login', () => {
            Game.loginScreenId = 2
            Game.loginUsername = AccountData.current?.username ?? '<EMAIL>'
            Game.loginPassword = AccountData.current?.password ?? 'account123'
        })  
        
        this.addDebugButton('Switch proxy', () => {
            ProxyHandler.pickNextProxy()
        }) 
        
        this.addDebugButton('Enable canvas', () => {
            Ui.buildCanvas()
        })
    }

    static addButtonToMain(text: string, clickHandler: (button?: any) => void) {
        this.addButton(text, clickHandler, this.layout)
    }

    static addDebugButton(text: string, clickHandler: () => void) {
        this.addButton(text, clickHandler, this.debugLayout)
    }

    static addButton(text: string, clickHandler: (button?: any) => void, parentLayout: any, textSize = -1) {
        text = text.toUpperCase()

        var Button = Java.use('android.widget.Button')
        var String = Java.use('java.lang.String')

        var button = Button.$new(this.context)
        button.setText(String.$new(text))
        button.setPadding(8, -2, 8, 1)
        button.setLeft(5)
        button.setTextSize(Ui.textSizeButtonRegular)

        button.setBackgroundColor(toJavaColor('#000000'))
        button.setTextColor(toJavaColor('#fbff00'))

        parentLayout.addView(button)
        this.views.push(button)

        const onClickRunnable = createOnClickListener(() => {
            clickHandler()
        })

        button.setOnClickListener(onClickRunnable)
    }

    static toggleDebugOption(flag: string) {
        BotScript.currentTextY = 75

        if (flag == 'isGameDebug') {
            Debuggers.isGameDebug = !Debuggers.isGameDebug
        }
        if (flag == 'isNpcsDebug') {
            Debuggers.isNpcsDebug = !Debuggers.isNpcsDebug
        }
        if (flag == 'isPlayersDebug') {
            Debuggers.isPlayersDebug = !Debuggers.isPlayersDebug
        }
        if (flag == 'isGameObjectsDebug') {
            Debuggers.isGameObjectsDebug = !Debuggers.isGameObjectsDebug
        }
        if (flag == 'isInventoryDebug') {
            Debuggers.isInventoryDebug = !Debuggers.isInventoryDebug
        }
        if (flag == 'isVarbitsDebug') {
            Debuggers.isVarbitsDebug = !Debuggers.isVarbitsDebug
        }
        if (flag == 'isCollisionFlagsDebug') {
            Debuggers.isCollisionFlagsDebug = !Debuggers.isCollisionFlagsDebug
        }
        if (flag == 'isDeviceInfoDebug') {
            Debuggers.isDeviceInfoDebug = !Debuggers.isDeviceInfoDebug
        }
        if (flag == 'isPersistentLogsDebug') {
            Debuggers.isPersistentLogsDebug = !Debuggers.isPersistentLogsDebug
        }
        if (flag == 'isFirebaseDebug') {
            Debuggers.isFirebaseDebug = !Debuggers.isFirebaseDebug
        }
        if (flag == 'isMemoryDebug') {
            Debuggers.isMemoryDebug = !Debuggers.isMemoryDebug
        }
        if (flag == 'isWidgetsDebug') {
            Debuggers.isWidgetsDebug = !Debuggers.isWidgetsDebug
        }
        if (flag == 'isGroundItemsDebug') {
            Debuggers.isGroundItemsDebug = !Debuggers.isGroundItemsDebug
        }
        log(`Toggled ${flag}`) // Placeholder for actual logic
    }

    static toggleDebugVisibility() {
        javaPerform(() => {
            scheduleOnMainThread(() => {
                if (Ui.debugLayout) {
                    const View = Java.use('android.view.View')
                    const isVisible = Ui.debugLayout.getVisibility() == View.VISIBLE.value
                    Ui.debugLayout.setVisibility(isVisible ? View.GONE.value : View.VISIBLE.value)
                }
            })
        })
    }

    static removeOldUi() {
        log('Scanning for views')

        Java.choose('android.widget.LinearLayout', {
            onMatch: function (instance) {
                log('Found view: ', instance)
                log('   => (tag): ', instance.getTag())
                log('   => (parent):  ', instance.getParent())
                log('   => (getLayoutDirection):  ', instance.getLayoutDirection())
                log('   => (getVisibility):  ', instance.getVisibility())
                scheduleOnMainThread(function () {
                    try {
                        const ViewManager = Java.use('android.view.ViewManager')
                        const systemService = Ui.getActivity().getSystemService('window')
                        const viewManager = Java.cast(systemService, ViewManager)
                        viewManager.removeView(instance)
                    } catch (e) {
                        log('Error removing view: ', instance.getTag(), '' + e)
                    }
                })
                // const activity = Ui.getActivity();
                // const WindowManager = Java.use('android.view.WindowManager');
                // const windowManager = Java.cast(activity.getSystemService("window"), WindowManager);
                // windowManager.removeViewImmediate(instance);
            },
            onComplete: function () {},
        })
    }

    static build() {
        this.buildButtons()
    }

    static getActivity(): any {
        const AndroidKeyboard = Java.use('com.jagex.android.AndroidKeyboard')
        const Activity = Java.use('android.app.Activity')
        const nativeActivityField = AndroidKeyboard.class.getDeclaredField('a')
        nativeActivityField.setAccessible(true)
        const activity = Java.cast(nativeActivityField.get(Java.use('java.lang.Object').$new()), Activity)
        return activity
    }

    static buildCanvas() {
        javaPerform(() => {
            scheduleOnMainThread(function () {
                log('buildCanvas')

                try {
                    log('Schedule on main thread start (buildCanvas) - 1')

                    const Color = Java.use('android.graphics.Color')
                    const Paint = Java.use('android.graphics.Paint')
                    const PixelFormat = Java.use('android.graphics.PixelFormat')
                    const ViewManager = Java.use('android.view.ViewManager')
                    const WindowManagerLayoutParams = Java.use('android.view.WindowManager$LayoutParams')
                    const View = Java.use('android.view.View')


                    const activity = Ui.getActivity()
                    const systemService = activity.getSystemService('window')
                    const viewManager = Java.cast(systemService, ViewManager)

                    try {
                        const layoutParams = WindowManagerLayoutParams.$new(60, 60, WindowManagerLayoutParams.WRAP_CONTENT.value)
                        layoutParams.format = PixelFormat.RGBA_8888.value


                        const mTestView = View.$new(activity)
                        // Set tag on the canvas overlay view for identification
                        mTestView.setTag('ui-injected-canvas')
                        const paint = Paint.$new(Paint.ANTI_ALIAS_FLAG.value)
                        paint.setColor(Color.WHITE.value)
                        paint.setTextSize(18)

                        mTestView.onDraw.overload('android.graphics.Canvas').implementation = function (canvas: any) {
                            CanvasHandler.onDraw(canvas, paint)
                        }

                        viewManager.addView(mTestView, layoutParams)
                        Ui.canvasView = mTestView

                        Ui.uiUpdateThread = startLoop(
                            () => {
                                javaPerform(() => {
                                    scheduleOnMainThread(() => {
                                        mTestView.invalidate()
                                    })
                                })
                            },
                            50,
                            'ui_update'
                        )
                    } catch (e) {
                        log('Overlay error:', e)
                    }
                } catch (e) {
                    log('ui.ts, ' + e)
                }
            })
        })
    }

    static buildButtons() {
        javaPerform(() => {
            scheduleOnMainThread(() => {
                const WindowManager = Java.use('android.view.WindowManager')
                const ViewManager = Java.use('android.view.ViewManager')
                const WindowManagerLayoutParams = Java.use('android.view.WindowManager$LayoutParams')
                var LinearLayout = Java.use('android.widget.LinearLayout')
                var ViewGroupLayoutParams = Java.use('android.view.ViewGroup$LayoutParams')
                var Gravity = Java.use('android.view.Gravity')
                const View = Java.use('android.view.View')

                const activity = Ui.getActivity()

                const windowManager = Java.cast(activity.getSystemService('window'), WindowManager)
                const viewManager = Java.cast(windowManager, ViewManager)
                var context = activity

                this.context = context
                this.views = [] // Clear existing views

                // 1. Create the top-left debug buttons container (initially visible)
                var debugLayout = LinearLayout.$new(context)
                debugLayout.setLayoutParams(ViewGroupLayoutParams.$new(ViewGroupLayoutParams.WRAP_CONTENT.value, ViewGroupLayoutParams.WRAP_CONTENT.value))
                debugLayout.setOrientation(LinearLayout.VERTICAL.value)
                debugLayout.setVisibility(View.GONE.value)
                // Set tag on the debug button container
                debugLayout.setTag('ui-injected-debugbuttons')
                this.debugLayout = debugLayout

                const screenSize = Ui.getScreenSize()

                const debugLayoutParams = WindowManagerLayoutParams.$new(
                    WindowManagerLayoutParams.WRAP_CONTENT.value,
                    WindowManagerLayoutParams.WRAP_CONTENT.value,
                    0, // x-position: top-left
                    10, // y-position: top-left
                    60,
                    WindowManagerLayoutParams.FLAG_NOT_FOCUSABLE.value | WindowManagerLayoutParams.FLAG_LAYOUT_IN_SCREEN.value | WindowManagerLayoutParams.FLAG_HARDWARE_ACCELERATED.value,
                    Java.use('android.graphics.PixelFormat').RGBA_8888.value
                )
                debugLayoutParams.gravity = Gravity.TOP.value | Gravity.LEFT.value
                debugLayoutParams.x = 0 // Ensure x is 0 for top-left
                debugLayoutParams.y = 0 // Ensure y is 0 for top-left

                viewManager.addView(debugLayout, debugLayoutParams)

                // 2. Create the bottom button container
                var layout = LinearLayout.$new(context)
                layout.setLayoutParams(ViewGroupLayoutParams.$new(ViewGroupLayoutParams.WRAP_CONTENT.value, ViewGroupLayoutParams.WRAP_CONTENT.value))
                layout.setOrientation(LinearLayout.HORIZONTAL.value)
                // Set tag on the main button container
                layout.setTag('ui-injected-bottombuttons')
                this.layout = layout
                // Track the injected view

                const layoutParams = WindowManagerLayoutParams.$new(
                    screenSize.width,
                    30,
                    0, // x-position, adjust as needed
                    (screenSize.height / 2) | 0, // y-position, near the bottom (adjust 40 based on button height)
                    60,
                    WindowManagerLayoutParams.FLAG_NOT_FOCUSABLE.value | WindowManagerLayoutParams.FLAG_LAYOUT_IN_SCREEN.value | WindowManagerLayoutParams.FLAG_HARDWARE_ACCELERATED.value,
                    Java.use('android.graphics.PixelFormat').RGBA_8888.value
                )

                layoutParams.gravity = Gravity.TOP.value | Gravity.LEFT.value
                layoutParams.x = 0 

                this.addUiComponents()

                viewManager.addView(layout, layoutParams)

                startNewThread(() => {
                    Time.sleep(6000)
                    javaPerform(() => {
                        scheduleOnMainThread(() => {
                            layout.invalidate()
                            debugLayout.invalidate()
                        })
                    })
                }, 'BuildButtonsInv')
            })
        })
    }

    static getScreenSize() {
        const cached = getCache('screenSize')
        if (cached != null) {
            return cached
        }

        const WindowManager = Java.use('android.view.WindowManager')
        const activity = Ui.getActivity()
        const windowManager = Java.cast(activity.getSystemService('window'), WindowManager)
        const DisplayMetrics = Java.use('android.util.DisplayMetrics')
        const displayMetrics = DisplayMetrics.$new()
        windowManager.getDefaultDisplay().getMetrics(displayMetrics)
        const screenWidth = displayMetrics.widthPixels.value
        const screenHeight = displayMetrics.heightPixels.value

        const size = {
            width: screenWidth,
            height: screenHeight,
        }

        putCache('screenSize', size)

        return size
    }

    static get textSizeRegular() {
        if (Ui.getScreenSize().width > 2600) {
            return 36
        }

        if (Ui.getScreenSize().width > 1200) {
            return 32
        }

        return 14
    }

    static get textSizeButtonRegular() {
        if (Ui.getScreenSize().width > 1200) {
            return 16
        }

        return 10
    }

    static removeCreatedUiElements() {
        javaPerform(() => {
            scheduleOnMainThread(() => {
                try {
                    const ViewManager = Java.use('android.view.ViewManager')
                    const activity = Ui.getActivity()
                    const viewManager = Java.cast(activity.getSystemService('window'), ViewManager)

                    // Stop the UI update thread if it exists
                    //  if (this.uiUpdateThread) {
                    //      this.uiUpdateThread.stop()
                    //      this.uiUpdateThread = null
                    //  }

                    // Remove the canvas view if it exists
                    if (this.canvasView) {
                        try {
                            viewManager.removeView(this.canvasView)
                            this.canvasView = null
                        } catch (e) {
                            log('Error removing canvas view: ' + e)
                        }
                    }

                    // Remove views we've tracked
                    if (this.views) {
                        this.views.forEach((view) => {
                            try {
                                if (view.getParent()) {
                                    viewManager.removeView(view)
                                }
                            } catch (e) {
                                log('Error removing tracked view: ' + e)
                            }
                        })
                        this.views = []
                    }

                    // Remove our main layout and debug layout
                    if (this.layout) {
                        try {
                            viewManager.removeView(this.layout)
                            this.layout = null
                        } catch (e) {
                            log('Error removing main layout: ' + e)
                        }
                    }

                    if (this.debugLayout) {
                        try {
                            viewManager.removeView(this.debugLayout)
                            this.debugLayout = null
                        } catch (e) {
                            log('Error removing debug layout: ' + e)
                        }
                    }

                    // Final cleanup of context
                    //this.context = null
                } catch (e) {
                    log('Error in removeCreatedUiElements: ' + e)
                }
            })
        })
    }
}
